using AcademicPerformance.Models.Entities;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AcademicPerformance.Migration
{
    /// <summary>
    /// Veri migrasyonu stratejisi ve implementasyonu
    /// </summary>
    public interface IDataMigrationService
    {
        Task<MigrationResult> MigrateToNewSystemAsync(MigrationOptions options);
        Task<bool> ValidateDataIntegrityAsync();
        Task<MigrationResult> RollbackMigrationAsync(string migrationId);
        Task<List<MigrationStatus>> GetMigrationStatusAsync();
    }

    /// <summary>
    /// Veri migrasyonu servisi implementasyonu
    /// </summary>
    public class DataMigrationService : IDataMigrationService
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<DataMigrationService> _logger;
        private readonly IPerformanceConfigurationService _configService;
        private readonly IReportingStore _legacyStore;

        public DataMigrationService(
            AcademicPerformanceDbContext context,
            ILogger<DataMigrationService> logger,
            IPerformanceConfigurationService configService,
            IReportingStore legacyStore)
        {
            _context = context;
            _logger = logger;
            _configService = configService;
            _legacyStore = legacyStore;
        }

        public async Task<MigrationResult> MigrateToNewSystemAsync(MigrationOptions options)
        {
            var migrationId = Guid.NewGuid().ToString();
            _logger.LogInformation("Starting data migration {MigrationId} with options: {@Options}", 
                migrationId, options);

            var result = new MigrationResult
            {
                MigrationId = migrationId,
                StartTime = DateTime.UtcNow,
                Options = options
            };

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // 1. Mevcut sabit konfigürasyonu dinamik konfigürasyona dönüştür
                await MigrateLegacyConfigurationAsync(result);

                // 2. Geçmiş performans verilerini yeni yapıya uyarla
                if (options.MigrateHistoricalData)
                {
                    await MigrateHistoricalPerformanceDataAsync(result);
                }

                // 3. Kullanıcı rollerini ve izinlerini güncelle
                await MigrateUserRolesAndPermissionsAsync(result);

                // 4. Raporlama verilerini yeni formata dönüştür
                await MigrateReportingDataAsync(result);

                // 5. Veri bütünlüğünü doğrula
                var validationResult = await ValidateDataIntegrityAsync();
                if (!validationResult)
                {
                    throw new InvalidOperationException("Data integrity validation failed");
                }

                await transaction.CommitAsync();

                result.IsSuccessful = true;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;

                _logger.LogInformation("Data migration {MigrationId} completed successfully in {Duration}", 
                    migrationId, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;

                _logger.LogError(ex, "Data migration {MigrationId} failed", migrationId);
                
                return result;
            }
        }

        private async Task MigrateLegacyConfigurationAsync(MigrationResult result)
        {
            _logger.LogInformation("Migrating legacy configuration to dynamic system");

            // Mevcut sabit değerleri al
            var legacyConfig = await ExtractLegacyConfigurationAsync();

            // Yeni dinamik konfigürasyon oluştur
            var newConfig = new CreateConfigurationDto
            {
                Name = "İstanbul Arel Üniversitesi - Migrated Configuration",
                Description = "Mevcut sistemden otomatik olarak dönüştürülmüş konfigürasyon",
                Version = "1.0.0",
                EffectiveStartDate = DateTime.UtcNow,
                CategoryWeights = new List<CategoryWeightDto>
                {
                    new() { CategoryId = "EDU", CategoryName = "Eğitim ve Öğretim", CategoryCode = "EDU", Weight = 0.30, RequiresNormalization = true, NormalizationType = "Ranking" },
                    new() { CategoryId = "ACA", CategoryName = "Akademik ve Bilimsel", CategoryCode = "ACA", Weight = 0.30, RequiresNormalization = true, NormalizationType = "Ranking" },
                    new() { CategoryId = "SOC", CategoryName = "Toplumsal Katkı", CategoryCode = "SOC", Weight = 0.10, RequiresNormalization = false },
                    new() { CategoryId = "STR", CategoryName = "Bölüm Stratejik Performans", CategoryCode = "STR", Weight = 0.20, RequiresNormalization = true, NormalizationType = "Ranking" },
                    new() { CategoryId = "MGR", CategoryName = "Yönetici Değerlendirmesi", CategoryCode = "MGR", Weight = 0.10, RequiresNormalization = false }
                },
                PerformanceClassifications = new List<PerformanceClassificationDto>
                {
                    new() { ClassificationCode = "A", ClassificationName = "Mükemmel", MinValue = 0.81, MaxValue = 1.00, ColorCode = "#4CAF50", IsSuccessful = true },
                    new() { ClassificationCode = "B", ClassificationName = "İyi", MinValue = 0.61, MaxValue = 0.80, ColorCode = "#8BC34A", IsSuccessful = true },
                    new() { ClassificationCode = "C", ClassificationName = "Orta", MinValue = 0.41, MaxValue = 0.60, ColorCode = "#FFC107", IsSuccessful = true },
                    new() { ClassificationCode = "D", ClassificationName = "Zayıf", MinValue = 0.21, MaxValue = 0.40, ColorCode = "#FF9800", IsSuccessful = false },
                    new() { ClassificationCode = "E", ClassificationName = "Yetersiz", MinValue = 0.00, MaxValue = 0.20, ColorCode = "#F44336", IsSuccessful = false, IsCritical = true }
                },
                MinimumScoreRequirements = new List<MinimumScoreRequirementDto>
                {
                    new() { CategoryId = "EDU", MinimumScore = 40, ScoreType = "Raw", IsMandatory = true, FailureAction = "RequireApproval" },
                    new() { CategoryId = "ACA", MinimumScore = 40, ScoreType = "Raw", IsMandatory = true, FailureAction = "RequireApproval" },
                    new() { CategoryId = "STR", MinimumScore = 40, ScoreType = "Raw", IsMandatory = true, FailureAction = "RequireApproval" },
                    new() { CategoryId = "MGR", MinimumScore = 40, ScoreType = "Raw", IsMandatory = true, FailureAction = "RequireApproval" }
                },
                CalculationFormulas = new List<CalculationFormulaDto>
                {
                    new() { FormulaName = "Genel Performans Skoru", FormulaType = "OverallScore", FormulaExpression = "WEIGHTED_SUM({categoryScores})", ApplicationScope = "Individual" },
                    new() { FormulaName = "Performans Endeksi", FormulaType = "PerformanceIndex", FormulaExpression = "PERFORMANCE_INDEX({rank}, {totalCount})", ApplicationScope = "Individual" },
                    new() { FormulaName = "Başarı Endeksi", FormulaType = "SuccessIndex", FormulaExpression = "SUCCESS_INDEX({rank}, {totalCount})", ApplicationScope = "Individual" }
                },
                EvaluationPeriods = new List<EvaluationPeriodDto>
                {
                    new() 
                    { 
                        PeriodName = "2024-2025 Akademik Yılı", 
                        AcademicYear = "2024-2025",
                        PeriodStartDate = new DateTime(2024, 5, 16),
                        PeriodEndDate = new DateTime(2025, 5, 15),
                        DataEntryStartDate = new DateTime(2025, 4, 1),
                        DataEntryEndDate = new DateTime(2025, 5, 15, 17, 0, 0),
                        ApprovalStartDate = new DateTime(2025, 5, 16),
                        ApprovalEndDate = new DateTime(2025, 5, 25),
                        CalculationDate = new DateTime(2025, 6, 1),
                        ResultAnnouncementDate = new DateTime(2025, 6, 20),
                        IsCurrentPeriod = true
                    }
                }
            };

            var createdConfig = await _configService.CreateConfigurationAsync(newConfig, "SYSTEM_MIGRATION");
            
            // Konfigürasyonu otomatik onayla
            await _configService.ApproveConfigurationAsync(createdConfig.Id, "SYSTEM_MIGRATION", "Automatic approval during migration");
            
            // Konfigürasyonu aktif hale getir
            await _configService.ActivateConfigurationAsync(createdConfig.Id, DateTime.UtcNow, "SYSTEM_MIGRATION");

            result.MigratedConfigurations.Add(createdConfig.Id);
            result.Steps.Add($"Legacy configuration migrated to dynamic configuration: {createdConfig.Id}");
        }

        private async Task<LegacyConfiguration> ExtractLegacyConfigurationAsync()
        {
            // Mevcut ReportingManager ve DepartmentPerformanceManager'dan sabit değerleri çıkar
            return new LegacyConfiguration
            {
                PerformanceLevels = new Dictionary<string, (double Min, double Max)>
                {
                    ["Excellent"] = (90, 100),
                    ["Good"] = (70, 89),
                    ["Average"] = (50, 69),
                    ["Poor"] = (0, 49)
                },
                CategoryWeights = new Dictionary<string, decimal>
                {
                    ["AcademicStaff"] = 0.25m,
                    ["Research"] = 0.20m,
                    ["Publication"] = 0.20m,
                    ["StudentSatisfaction"] = 0.15m,
                    ["Infrastructure"] = 0.10m,
                    ["BudgetEfficiency"] = 0.10m
                }
            };
        }

        private async Task MigrateHistoricalPerformanceDataAsync(MigrationResult result)
        {
            _logger.LogInformation("Migrating historical performance data");

            // Geçmiş performans verilerini yeni yapıya uyarla
            var historicalData = await _legacyStore.GetAllHistoricalDataAsync();
            
            foreach (var data in historicalData)
            {
                // Eski performans seviyelerini yeni sınıflandırmaya dönüştür
                var newClassification = ConvertLegacyPerformanceLevel(data.PerformanceLevel);
                
                // Veriyi yeni formatta kaydet
                await SaveMigratedPerformanceDataAsync(data, newClassification);
            }

            result.MigratedRecords += historicalData.Count;
            result.Steps.Add($"Migrated {historicalData.Count} historical performance records");
        }

        private string ConvertLegacyPerformanceLevel(string legacyLevel)
        {
            return legacyLevel switch
            {
                "Excellent" => "A",
                "Good" => "B", 
                "Average" => "C",
                "Poor" => "D",
                _ => "E"
            };
        }

        public async Task<bool> ValidateDataIntegrityAsync()
        {
            _logger.LogInformation("Validating data integrity after migration");

            try
            {
                // 1. Konfigürasyon bütünlüğünü kontrol et
                var activeConfig = await _configService.GetActiveConfigurationAsync(DateTime.UtcNow);
                if (activeConfig == null)
                {
                    _logger.LogError("No active configuration found after migration");
                    return false;
                }

                // 2. Kategori ağırlıklarının toplamının 1.0 olduğunu kontrol et
                var totalWeight = activeConfig.CategoryWeights.Sum(cw => cw.Weight);
                if (Math.Abs((double)totalWeight - 1.0) > 0.001)
                {
                    _logger.LogError("Category weights do not sum to 1.0. Total: {TotalWeight}", totalWeight);
                    return false;
                }

                // 3. Performans sınıflandırmalarının aralıklarını kontrol et
                var classifications = activeConfig.PerformanceClassifications.OrderBy(pc => pc.MinValue).ToList();
                for (int i = 0; i < classifications.Count - 1; i++)
                {
                    if (classifications[i].MaxValue != classifications[i + 1].MinValue)
                    {
                        _logger.LogError("Performance classification ranges have gaps or overlaps");
                        return false;
                    }
                }

                // 4. Minimum puan gereksinimlerini kontrol et
                foreach (var requirement in activeConfig.MinimumScoreRequirements)
                {
                    if (requirement.MinimumScore < 0 || requirement.MinimumScore > 100)
                    {
                        _logger.LogError("Invalid minimum score requirement: {Score}", requirement.MinimumScore);
                        return false;
                    }
                }

                _logger.LogInformation("Data integrity validation passed");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data integrity validation");
                return false;
            }
        }

        // Diğer migration methodları...
    }

    #region Supporting Classes

    public class MigrationResult
    {
        public string MigrationId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public MigrationOptions Options { get; set; } = new();
        public List<string> MigratedConfigurations { get; set; } = new();
        public int MigratedRecords { get; set; }
        public List<string> Steps { get; set; } = new();
    }

    public class MigrationOptions
    {
        public bool MigrateHistoricalData { get; set; } = true;
        public bool PreserveOriginalData { get; set; } = true;
        public bool ValidateAfterMigration { get; set; } = true;
        public DateTime? MigrationCutoffDate { get; set; }
        public List<string> IncludedDepartments { get; set; } = new();
        public bool DryRun { get; set; } = false;
    }

    public class LegacyConfiguration
    {
        public Dictionary<string, (double Min, double Max)> PerformanceLevels { get; set; } = new();
        public Dictionary<string, decimal> CategoryWeights { get; set; } = new();
    }

    #endregion
}
