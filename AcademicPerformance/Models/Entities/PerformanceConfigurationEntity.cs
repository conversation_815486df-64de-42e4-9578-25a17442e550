using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Entities
{
    /// <summary>
    /// Performans değerlendirme ana konfigürasyon entity'si
    /// </summary>
    public class PerformanceConfigurationEntity : EntityBaseModel
    {
        /// <summary>
        /// Konfigürasyon adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Konfigürasyon açıklaması
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Konfigürasyon versiyonu
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime EffectiveStartDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveEndDate { get; set; }

        /// <summary>
        /// Onay durumu
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApprovalStatus { get; set; } = "Draft"; // Draft, Pending, Approved, Rejected

        /// <summary>
        /// Onaylayan kullanıcı ID'si
        /// </summary>
        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// Onay tarihi
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Son güncelleyen kullanıcı ID'si
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// Güncelleme tarihi
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        /// <summary>
        /// Kategori ağırlıkları
        /// </summary>
        public virtual ICollection<CategoryWeightEntity> CategoryWeights { get; set; } = new List<CategoryWeightEntity>();

        /// <summary>
        /// Performans sınıflandırma kriterleri
        /// </summary>
        public virtual ICollection<PerformanceClassificationEntity> PerformanceClassifications { get; set; } = new List<PerformanceClassificationEntity>();

        /// <summary>
        /// Minimum puan gereksinimleri
        /// </summary>
        public virtual ICollection<MinimumScoreRequirementEntity> MinimumScoreRequirements { get; set; } = new List<MinimumScoreRequirementEntity>();

        /// <summary>
        /// Hesaplama formül konfigürasyonları
        /// </summary>
        public virtual ICollection<CalculationFormulaEntity> CalculationFormulas { get; set; } = new List<CalculationFormulaEntity>();

        /// <summary>
        /// Değerlendirme dönem konfigürasyonları
        /// </summary>
        public virtual ICollection<EvaluationPeriodEntity> EvaluationPeriods { get; set; } = new List<EvaluationPeriodEntity>();
    }
}
