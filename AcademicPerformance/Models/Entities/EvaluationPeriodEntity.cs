using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Entities
{
    /// <summary>
    /// Değerlendirme dönemi konfigürasyon entity'si
    /// </summary>
    public class EvaluationPeriodEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformanceConfigurationId { get; set; } = string.Empty;

        /// <summary>
        /// Dönem adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public string PeriodName { get; set; } = string.Empty;

        /// <summary>
        /// Akademik yıl
        /// </summary>
        [Required]
        [StringLength(20)]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// Dönem başlangıç tarihi
        /// </summary>
        public DateTime PeriodStartDate { get; set; }

        /// <summary>
        /// Dönem bitiş tarihi
        /// </summary>
        public DateTime PeriodEndDate { get; set; }

        /// <summary>
        /// Veri girişi başlangıç tarihi
        /// </summary>
        public DateTime DataEntryStartDate { get; set; }

        /// <summary>
        /// Veri girişi bitiş tarihi
        /// </summary>
        public DateTime DataEntryEndDate { get; set; }

        /// <summary>
        /// Onay süreci başlangıç tarihi
        /// </summary>
        public DateTime ApprovalStartDate { get; set; }

        /// <summary>
        /// Onay süreci bitiş tarihi
        /// </summary>
        public DateTime ApprovalEndDate { get; set; }

        /// <summary>
        /// Sonuç hesaplama tarihi
        /// </summary>
        public DateTime CalculationDate { get; set; }

        /// <summary>
        /// Sonuç bildirimi tarihi
        /// </summary>
        public DateTime ResultAnnouncementDate { get; set; }

        /// <summary>
        /// İtiraz başlangıç tarihi
        /// </summary>
        public DateTime? AppealStartDate { get; set; }

        /// <summary>
        /// İtiraz bitiş tarihi
        /// </summary>
        public DateTime? AppealEndDate { get; set; }

        /// <summary>
        /// Dönem durumu (Planning, Active, DataEntry, Approval, Calculation, Completed, Archived)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PeriodStatus { get; set; } = "Planning";

        /// <summary>
        /// Dönem açıklaması
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Mevcut dönem mi?
        /// </summary>
        public bool IsCurrentPeriod { get; set; } = false;

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu
        /// </summary>
        public virtual PerformanceConfigurationEntity PerformanceConfiguration { get; set; } = null!;

        /// <summary>
        /// Bu döneme ait önemli tarihler
        /// </summary>
        public virtual ICollection<PeriodMilestoneEntity> PeriodMilestones { get; set; } = new List<PeriodMilestoneEntity>();
    }

    /// <summary>
    /// Dönem kilometre taşları
    /// </summary>
    public class PeriodMilestoneEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu değerlendirme dönemi ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string EvaluationPeriodId { get; set; } = string.Empty;

        /// <summary>
        /// Kilometre taşı adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public string MilestoneName { get; set; } = string.Empty;

        /// <summary>
        /// Kilometre taşı türü (Deadline, Notification, Process)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MilestoneType { get; set; } = string.Empty;

        /// <summary>
        /// Hedef tarih
        /// </summary>
        public DateTime TargetDate { get; set; }

        /// <summary>
        /// Açıklama
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Tamamlandı mı?
        /// </summary>
        public bool IsCompleted { get; set; } = false;

        /// <summary>
        /// Tamamlanma tarihi
        /// </summary>
        public DateTime? CompletedDate { get; set; }

        /// <summary>
        /// Bildirim gönderilsin mi?
        /// </summary>
        public bool SendNotification { get; set; } = false;

        /// <summary>
        /// Bildirim gönderilme tarihi
        /// </summary>
        public DateTime? NotificationSentDate { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu değerlendirme dönemi
        /// </summary>
        public virtual EvaluationPeriodEntity EvaluationPeriod { get; set; } = null!;
    }
}
