using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AcademicPerformance.Models.Entities
{
    /// <summary>
    /// Minimum puan gereksinimi konfigürasyon entity'si
    /// </summary>
    public class MinimumScoreRequirementEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformanceConfigurationId { get; set; } = string.Empty;

        /// <summary>
        /// Bağlı olduğu kategori ağırlık ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CategoryWeightId { get; set; } = string.Empty;

        /// <summary>
        /// Minimum puan değeri
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal MinimumScore { get; set; }

        /// <summary>
        /// <PERSON>uan türü (Raw, Normalized, Percentage)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ScoreType { get; set; } = "Raw";

        /// <summary>
        /// Gereksinim açıklaması
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Zorunlu mu?
        /// </summary>
        public bool IsMandatory { get; set; } = true;

        /// <summary>
        /// Bu gereksinimi karşılamama durumunda aksiyon
        /// </summary>
        [StringLength(100)]
        public string? FailureAction { get; set; } // "AutoClassifyAsE", "RequireApproval", "Warning"

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime EffectiveStartDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveEndDate { get; set; }

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu
        /// </summary>
        public virtual PerformanceConfigurationEntity PerformanceConfiguration { get; set; } = null!;

        /// <summary>
        /// Bağlı olduğu kategori ağırlığı
        /// </summary>
        public virtual CategoryWeightEntity CategoryWeight { get; set; } = null!;

        /// <summary>
        /// Bu gereksinime ait istisnalar
        /// </summary>
        public virtual ICollection<ScoreRequirementExceptionEntity> Exceptions { get; set; } = new List<ScoreRequirementExceptionEntity>();
    }

    /// <summary>
    /// Minimum puan gereksinimi istisnaları
    /// </summary>
    public class ScoreRequirementExceptionEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu minimum puan gereksinimi ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string MinimumScoreRequirementId { get; set; } = string.Empty;

        /// <summary>
        /// İstisna türü (AcademicTitle, Department, SpecialCircumstance)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ExceptionType { get; set; } = string.Empty;

        /// <summary>
        /// İstisna değeri (Profesör, Doçent, vb.)
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ExceptionValue { get; set; } = string.Empty;

        /// <summary>
        /// İstisna durumunda minimum puan
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal ExceptionMinimumScore { get; set; }

        /// <summary>
        /// İstisna açıklaması
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime EffectiveStartDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveEndDate { get; set; }

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu minimum puan gereksinimi
        /// </summary>
        public virtual MinimumScoreRequirementEntity MinimumScoreRequirement { get; set; } = null!;
    }
}
