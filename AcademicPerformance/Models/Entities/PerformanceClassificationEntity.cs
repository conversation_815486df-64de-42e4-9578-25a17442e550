using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AcademicPerformance.Models.Entities
{
    /// <summary>
    /// Performans sınıflandırma konfigürasyon entity'si
    /// </summary>
    public class PerformanceClassificationEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformanceConfigurationId { get; set; } = string.Empty;

        /// <summary>
        /// Sınıflandırma kodu (A, B, C, D, E)
        /// </summary>
        [Required]
        [StringLength(5)]
        public string ClassificationCode { get; set; } = string.Empty;

        /// <summary>
        /// Sınıflandırma adı
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ClassificationName { get; set; } = string.Empty;

        /// <summary>
        /// Minimum değer (dahil)
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal MinValue { get; set; }

        /// <summary>
        /// Maksimum değer (dahil)
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal MaxValue { get; set; }

        /// <summary>
        /// Açıklama
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Renk kodu (UI için)
        /// </summary>
        [StringLength(7)]
        public string? ColorCode { get; set; }

        /// <summary>
        /// Sıralama (yüksekten düşüğe)
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Başarılı kabul edilir mi?
        /// </summary>
        public bool IsSuccessful { get; set; } = true;

        /// <summary>
        /// Kritik seviye mi? (E kategorisi gibi)
        /// </summary>
        public bool IsCritical { get; set; } = false;

        /// <summary>
        /// Bu seviyedeki personel için özel kurallar
        /// </summary>
        [StringLength(1000)]
        public string? SpecialRules { get; set; }

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu
        /// </summary>
        public virtual PerformanceConfigurationEntity PerformanceConfiguration { get; set; } = null!;

        /// <summary>
        /// Bu sınıflandırmaya ait özel aksiyonlar
        /// </summary>
        public virtual ICollection<ClassificationActionEntity> ClassificationActions { get; set; } = new List<ClassificationActionEntity>();
    }

    /// <summary>
    /// Sınıflandırma aksiyonları (ücret artışı, sözleşme yenileme vb.)
    /// </summary>
    public class ClassificationActionEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu sınıflandırma ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformanceClassificationId { get; set; } = string.Empty;

        /// <summary>
        /// Aksiyon türü (SalaryIncrease, ContractRenewal, Support, Warning)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// Aksiyon açıklaması
        /// </summary>
        [Required]
        [StringLength(500)]
        public string ActionDescription { get; set; } = string.Empty;

        /// <summary>
        /// Aksiyon değeri (ücret artış oranı vb.)
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? ActionValue { get; set; }

        /// <summary>
        /// Otomatik mi yoksa manuel onay mı?
        /// </summary>
        public bool IsAutomatic { get; set; } = false;

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu performans sınıflandırması
        /// </summary>
        public virtual PerformanceClassificationEntity PerformanceClassification { get; set; } = null!;
    }
}
