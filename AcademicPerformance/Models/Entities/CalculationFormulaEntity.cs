using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Entities
{
    /// <summary>
    /// Hesaplama formülü konfigürasyon entity'si
    /// </summary>
    public class CalculationFormulaEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformanceConfigurationId { get; set; } = string.Empty;

        /// <summary>
        /// Formül adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public string FormulaName { get; set; } = string.Empty;

        /// <summary>
        /// Formül türü (OverallScore, CategoryNormalization, PerformanceIndex, SuccessIndex)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FormulaType { get; set; } = string.Empty;

        /// <summary>
        /// Formül ifadesi (matematiksel ifade)
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string FormulaExpression { get; set; } = string.Empty;

        /// <summary>
        /// Formül açıklaması
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Formül parametreleri (JSON format)
        /// </summary>
        [StringLength(4000)]
        public string? Parameters { get; set; }

        /// <summary>
        /// Uygulama kapsamı (Individual, Department, Faculty, University)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApplicationScope { get; set; } = "Individual";

        /// <summary>
        /// Hesaplama sırası
        /// </summary>
        public int CalculationOrder { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Geçerlilik başlangıç tarihi
        /// </summary>
        public DateTime EffectiveStartDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Geçerlilik bitiş tarihi
        /// </summary>
        public DateTime? EffectiveEndDate { get; set; }

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu
        /// </summary>
        public virtual PerformanceConfigurationEntity PerformanceConfiguration { get; set; } = null!;

        /// <summary>
        /// Formül değişkenleri
        /// </summary>
        public virtual ICollection<FormulaVariableEntity> FormulaVariables { get; set; } = new List<FormulaVariableEntity>();
    }

    /// <summary>
    /// Formül değişkenleri
    /// </summary>
    public class FormulaVariableEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu hesaplama formülü ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CalculationFormulaId { get; set; } = string.Empty;

        /// <summary>
        /// Değişken adı
        /// </summary>
        [Required]
        [StringLength(100)]
        public string VariableName { get; set; } = string.Empty;

        /// <summary>
        /// Değişken türü (CategoryScore, TotalPersonnel, Rank, etc.)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string VariableType { get; set; } = string.Empty;

        /// <summary>
        /// Değişken kaynağı (Database, Calculation, Constant)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string VariableSource { get; set; } = string.Empty;

        /// <summary>
        /// Kaynak sorgusu veya hesaplama yöntemi
        /// </summary>
        [StringLength(1000)]
        public string? SourceQuery { get; set; }

        /// <summary>
        /// Varsayılan değer
        /// </summary>
        [StringLength(100)]
        public string? DefaultValue { get; set; }

        /// <summary>
        /// Değişken açıklaması
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu hesaplama formülü
        /// </summary>
        public virtual CalculationFormulaEntity CalculationFormula { get; set; } = null!;
    }
}
