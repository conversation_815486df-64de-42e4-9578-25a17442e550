using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AcademicPerformance.Models.Entities
{
    /// <summary>
    /// Kategori ağırlık konfigürasyon entity'si
    /// </summary>
    public class CategoryWeightEntity : EntityBaseModel
    {
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformanceConfigurationId { get; set; } = string.Empty;

        /// <summary>
        /// Kategori ID'si
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Kategori adı
        /// </summary>
        [Required]
        [StringLength(200)]
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// Kategori kodu (örn: EDU, ACA, SOC, STR, MGR)
        /// </summary>
        [Required]
        [StringLength(10)]
        public string CategoryCode { get; set; } = string.Empty;

        /// <summary>
        /// Kategori ağırlığı (0.0 - 1.0 arası)
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal Weight { get; set; }

        /// <summary>
        /// Kategori ağırlığı yüzde olarak (0-100)
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal WeightPercentage { get; set; }

        /// <summary>
        /// Kategori açıklaması
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Sıralama
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Aktif mi?
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Normalizasyon gerekli mi?
        /// </summary>
        public bool RequiresNormalization { get; set; } = false;

        /// <summary>
        /// Normalizasyon türü (Ranking, ZScore, MinMax)
        /// </summary>
        [StringLength(50)]
        public string? NormalizationType { get; set; }

        // Navigation Properties
        /// <summary>
        /// Bağlı olduğu performans konfigürasyonu
        /// </summary>
        public virtual PerformanceConfigurationEntity PerformanceConfiguration { get; set; } = null!;

        /// <summary>
        /// Bu kategoriye ait minimum puan gereksinimleri
        /// </summary>
        public virtual ICollection<MinimumScoreRequirementEntity> MinimumScoreRequirements { get; set; } = new List<MinimumScoreRequirementEntity>();
    }
}
