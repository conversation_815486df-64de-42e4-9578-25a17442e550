using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Entities;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Performans konfigürasyon yönetim servisi interface'i
    /// </summary>
    public interface IPerformanceConfigurationService
    {
        #region Configuration Management

        /// <summary>
        /// Aktif konfigürasyonu getir
        /// </summary>
        /// <param name="effectiveDate">Geçerlilik tarihi</param>
        /// <returns>Aktif konfigürasyon</returns>
        Task<PerformanceConfigurationEntity?> GetActiveConfigurationAsync(DateTime effectiveDate);

        /// <summary>
        /// Konfigürasyonu ID ile getir
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <returns>Konfigürasyon</returns>
        Task<PerformanceConfigurationEntity?> GetConfigurationByIdAsync(string configurationId);

        /// <summary>
        /// Tüm konfigürasyonları listele
        /// </summary>
        /// <param name="includeInactive">Pasif konfigürasyonlar dahil edilsin mi?</param>
        /// <returns>Konfigürasyon listesi</returns>
        Task<List<PerformanceConfigurationEntity>> GetAllConfigurationsAsync(bool includeInactive = false);

        /// <summary>
        /// Yeni konfigürasyon oluştur
        /// </summary>
        /// <param name="configuration">Konfigürasyon bilgileri</param>
        /// <param name="createdBy">Oluşturan kullanıcı ID'si</param>
        /// <returns>Oluşturulan konfigürasyon</returns>
        Task<PerformanceConfigurationEntity> CreateConfigurationAsync(
            CreateConfigurationDto configuration, 
            string createdBy);

        /// <summary>
        /// Konfigürasyonu güncelle
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="configuration">Güncellenmiş konfigürasyon bilgileri</param>
        /// <param name="updatedBy">Güncelleyen kullanıcı ID'si</param>
        /// <returns>Güncellenmiş konfigürasyon</returns>
        Task<PerformanceConfigurationEntity> UpdateConfigurationAsync(
            string configurationId, 
            UpdateConfigurationDto configuration, 
            string updatedBy);

        /// <summary>
        /// Konfigürasyonu sil (soft delete)
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="deletedBy">Silen kullanıcı ID'si</param>
        /// <returns>İşlem başarılı mı?</returns>
        Task<bool> DeleteConfigurationAsync(string configurationId, string deletedBy);

        #endregion

        #region Version Management

        /// <summary>
        /// Konfigürasyonun yeni versiyonunu oluştur
        /// </summary>
        /// <param name="sourceConfigurationId">Kaynak konfigürasyon ID'si</param>
        /// <param name="versionInfo">Versiyon bilgileri</param>
        /// <param name="createdBy">Oluşturan kullanıcı ID'si</param>
        /// <returns>Yeni versiyon</returns>
        Task<PerformanceConfigurationEntity> CreateVersionAsync(
            string sourceConfigurationId, 
            CreateVersionDto versionInfo, 
            string createdBy);

        /// <summary>
        /// Konfigürasyonun tüm versiyonlarını getir
        /// </summary>
        /// <param name="configurationName">Konfigürasyon adı</param>
        /// <returns>Versiyon listesi</returns>
        Task<List<PerformanceConfigurationEntity>> GetConfigurationVersionsAsync(string configurationName);

        /// <summary>
        /// İki versiyon arasındaki farkları getir
        /// </summary>
        /// <param name="version1Id">İlk versiyon ID'si</param>
        /// <param name="version2Id">İkinci versiyon ID'si</param>
        /// <returns>Fark analizi</returns>
        Task<ConfigurationDiffDto> GetVersionDifferenceAsync(string version1Id, string version2Id);

        #endregion

        #region Approval Workflow

        /// <summary>
        /// Konfigürasyonu onaya gönder
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="submittedBy">Gönderen kullanıcı ID'si</param>
        /// <param name="approvers">Onaylayıcı kullanıcı ID'leri</param>
        /// <returns>Onay süreci başlatıldı mı?</returns>
        Task<bool> SubmitForApprovalAsync(
            string configurationId, 
            string submittedBy, 
            List<string> approvers);

        /// <summary>
        /// Konfigürasyonu onayla
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="approvedBy">Onaylayan kullanıcı ID'si</param>
        /// <param name="comments">Onay yorumları</param>
        /// <returns>Onay başarılı mı?</returns>
        Task<bool> ApproveConfigurationAsync(
            string configurationId, 
            string approvedBy, 
            string? comments = null);

        /// <summary>
        /// Konfigürasyonu reddet
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="rejectedBy">Reddeden kullanıcı ID'si</param>
        /// <param name="reason">Red nedeni</param>
        /// <returns>Red işlemi başarılı mı?</returns>
        Task<bool> RejectConfigurationAsync(
            string configurationId, 
            string rejectedBy, 
            string reason);

        /// <summary>
        /// Onay bekleyen konfigürasyonları getir
        /// </summary>
        /// <param name="approverId">Onaylayıcı kullanıcı ID'si</param>
        /// <returns>Onay bekleyen konfigürasyonlar</returns>
        Task<List<PerformanceConfigurationEntity>> GetPendingApprovalsAsync(string approverId);

        #endregion

        #region Activation Management

        /// <summary>
        /// Konfigürasyonu aktif hale getir
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="effectiveDate">Geçerlilik başlangıç tarihi</param>
        /// <param name="activatedBy">Aktif hale getiren kullanıcı ID'si</param>
        /// <returns>Aktivasyon başarılı mı?</returns>
        Task<bool> ActivateConfigurationAsync(
            string configurationId, 
            DateTime effectiveDate, 
            string activatedBy);

        /// <summary>
        /// Konfigürasyonu pasif hale getir
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="deactivatedBy">Pasif hale getiren kullanıcı ID'si</param>
        /// <returns>Deaktivasyon başarılı mı?</returns>
        Task<bool> DeactivateConfigurationAsync(string configurationId, string deactivatedBy);

        #endregion

        #region Validation

        /// <summary>
        /// Konfigürasyonu doğrula
        /// </summary>
        /// <param name="configuration">Doğrulanacak konfigürasyon</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<ConfigurationValidationResultDto> ValidateConfigurationAsync(
            PerformanceConfigurationEntity configuration);

        /// <summary>
        /// Konfigürasyon çakışmalarını kontrol et
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="effectiveStartDate">Geçerlilik başlangıç tarihi</param>
        /// <param name="effectiveEndDate">Geçerlilik bitiş tarihi</param>
        /// <returns>Çakışma var mı?</returns>
        Task<bool> HasConfigurationConflictAsync(
            string configurationId, 
            DateTime effectiveStartDate, 
            DateTime? effectiveEndDate);

        #endregion

        #region Cache Management

        /// <summary>
        /// Konfigürasyon cache'ini temizle
        /// </summary>
        /// <param name="configurationId">Belirli bir konfigürasyon ID'si (null ise tümü)</param>
        /// <returns>Cache temizlendi mi?</returns>
        Task<bool> ClearConfigurationCacheAsync(string? configurationId = null);

        /// <summary>
        /// Cache istatistiklerini getir
        /// </summary>
        /// <returns>Cache istatistikleri</returns>
        Task<CacheStatisticsDto> GetCacheStatisticsAsync();

        #endregion

        #region Import/Export

        /// <summary>
        /// Konfigürasyonu JSON formatında export et
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <returns>JSON string</returns>
        Task<string> ExportConfigurationAsync(string configurationId);

        /// <summary>
        /// JSON formatından konfigürasyon import et
        /// </summary>
        /// <param name="jsonData">JSON veri</param>
        /// <param name="importedBy">Import eden kullanıcı ID'si</param>
        /// <returns>Import edilen konfigürasyon</returns>
        Task<PerformanceConfigurationEntity> ImportConfigurationAsync(string jsonData, string importedBy);

        #endregion
    }

    #region Supporting DTOs

    /// <summary>
    /// Konfigürasyon oluşturma DTO'su
    /// </summary>
    public class CreateConfigurationDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Version { get; set; } = "1.0.0";
        public DateTime EffectiveStartDate { get; set; } = DateTime.UtcNow;
        public DateTime? EffectiveEndDate { get; set; }
        public List<CategoryWeightDto> CategoryWeights { get; set; } = new();
        public List<PerformanceClassificationDto> PerformanceClassifications { get; set; } = new();
        public List<MinimumScoreRequirementDto> MinimumScoreRequirements { get; set; } = new();
        public List<CalculationFormulaDto> CalculationFormulas { get; set; } = new();
        public List<EvaluationPeriodDto> EvaluationPeriods { get; set; } = new();
    }

    /// <summary>
    /// Konfigürasyon güncelleme DTO'su
    /// </summary>
    public class UpdateConfigurationDto : CreateConfigurationDto
    {
        public string? UpdateReason { get; set; }
    }

    /// <summary>
    /// Versiyon oluşturma DTO'su
    /// </summary>
    public class CreateVersionDto
    {
        public string Version { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? ChangeLog { get; set; }
        public bool CopyAllSettings { get; set; } = true;
    }

    /// <summary>
    /// Konfigürasyon fark analizi DTO'su
    /// </summary>
    public class ConfigurationDiffDto
    {
        public string Version1Id { get; set; } = string.Empty;
        public string Version2Id { get; set; } = string.Empty;
        public List<ConfigurationChangeDto> Changes { get; set; } = new();
        public DateTime ComparedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Konfigürasyon değişiklik DTO'su
    /// </summary>
    public class ConfigurationChangeDto
    {
        public string ChangeType { get; set; } = string.Empty; // Added, Modified, Deleted
        public string Section { get; set; } = string.Empty; // CategoryWeights, Classifications, etc.
        public string PropertyName { get; set; } = string.Empty;
        public object? OldValue { get; set; }
        public object? NewValue { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// Konfigürasyon doğrulama sonucu DTO'su
    /// </summary>
    public class ConfigurationValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<ValidationErrorDto> Errors { get; set; } = new();
        public List<ValidationWarningDto> Warnings { get; set; } = new();
    }

    /// <summary>
    /// Doğrulama hatası DTO'su
    /// </summary>
    public class ValidationErrorDto
    {
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Section { get; set; } = string.Empty;
        public string? PropertyName { get; set; }
    }

    /// <summary>
    /// Doğrulama uyarısı DTO'su
    /// </summary>
    public class ValidationWarningDto
    {
        public string WarningCode { get; set; } = string.Empty;
        public string WarningMessage { get; set; } = string.Empty;
        public string Section { get; set; } = string.Empty;
        public string? PropertyName { get; set; }
    }

    /// <summary>
    /// Cache istatistikleri DTO'su
    /// </summary>
    public class CacheStatisticsDto
    {
        public int TotalCachedConfigurations { get; set; }
        public int ActiveConfigurationsCached { get; set; }
        public double CacheHitRatio { get; set; }
        public DateTime LastCacheRefresh { get; set; }
        public long TotalMemoryUsage { get; set; }
    }

    #endregion
}
