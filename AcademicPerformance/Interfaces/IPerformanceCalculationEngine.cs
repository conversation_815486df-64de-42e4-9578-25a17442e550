using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Entities;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Generic performans hesaplama motoru interface'i
    /// </summary>
    public interface IPerformanceCalculationEngine
    {
        #region Configuration Management

        /// <summary>
        /// Aktif performans konfigürasyonunu getir
        /// </summary>
        /// <param name="effectiveDate">Geçerlilik tarihi</param>
        /// <returns>Aktif konfigürasyon</returns>
        Task<PerformanceConfigurationEntity?> GetActiveConfigurationAsync(DateTime? effectiveDate = null);

        /// <summary>
        /// Belirli bir konfigürasyonu getir
        /// </summary>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <returns>Konfigürasyon</returns>
        Task<PerformanceConfigurationEntity?> GetConfigurationAsync(string configurationId);

        #endregion

        #region Score Calculations

        /// <summary>
        /// Akademisyen için kategori skorlarını hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kategori skorları</returns>
        Task<List<CategoryPerformanceDto>> CalculateCategoryScoresAsync(
            string academicianUserId, 
            string configurationId, 
            DateTime startDate, 
            DateTime endDate);

        /// <summary>
        /// Akademisyen için genel performans skorunu hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Genel performans skoru</returns>
        Task<OverallPerformanceDto> CalculateOverallScoreAsync(
            string academicianUserId, 
            string configurationId, 
            DateTime startDate, 
            DateTime endDate);

        /// <summary>
        /// Bölüm için normalizasyon hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Normalize edilmiş skorlar</returns>
        Task<List<NormalizedPerformanceDto>> CalculateDepartmentNormalizationAsync(
            string departmentId, 
            string configurationId, 
            DateTime startDate, 
            DateTime endDate);

        #endregion

        #region Classification and Validation

        /// <summary>
        /// Performans seviyesini belirle
        /// </summary>
        /// <param name="performanceIndex">Performans endeksi</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <returns>Performans sınıflandırması</returns>
        Task<PerformanceClassificationDto> DeterminePerformanceLevelAsync(
            double performanceIndex, 
            string configurationId);

        /// <summary>
        /// Minimum puan gereksinimlerini kontrol et
        /// </summary>
        /// <param name="categoryScores">Kategori skorları</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <returns>Gereksinim kontrol sonucu</returns>
        Task<MinimumScoreValidationDto> ValidateMinimumScoreRequirementsAsync(
            List<CategoryPerformanceDto> categoryScores, 
            string configurationId, 
            string academicianUserId);

        #endregion

        #region Formula Execution

        /// <summary>
        /// Belirli bir formülü çalıştır
        /// </summary>
        /// <param name="formulaType">Formül türü</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="parameters">Formül parametreleri</param>
        /// <returns>Hesaplama sonucu</returns>
        Task<FormulaExecutionResultDto> ExecuteFormulaAsync(
            string formulaType, 
            string configurationId, 
            Dictionary<string, object> parameters);

        #endregion

        #region Batch Operations

        /// <summary>
        /// Bölüm için toplu performans hesaplama
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="configurationId">Konfigürasyon ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Toplu hesaplama sonucu</returns>
        Task<BatchCalculationResultDto> CalculateDepartmentPerformanceBatchAsync(
            string departmentId, 
            string configurationId, 
            DateTime startDate, 
            DateTime endDate);

        #endregion
    }

    #region Supporting DTOs

    /// <summary>
    /// Genel performans DTO'su
    /// </summary>
    public class OverallPerformanceDto
    {
        public string AcademicianUserId { get; set; } = string.Empty;
        public double WeightedScore { get; set; }
        public double PerformanceIndex { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public List<CategoryPerformanceDto> CategoryScores { get; set; } = new();
        public bool MeetsMinimumRequirements { get; set; }
        public List<string> FailedRequirements { get; set; } = new();
        public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Normalize edilmiş performans DTO'su
    /// </summary>
    public class NormalizedPerformanceDto
    {
        public string AcademicianUserId { get; set; } = string.Empty;
        public string CategoryId { get; set; } = string.Empty;
        public double RawScore { get; set; }
        public double NormalizedScore { get; set; }
        public int Rank { get; set; }
        public int TotalCount { get; set; }
        public double SuccessIndex { get; set; }
    }

    /// <summary>
    /// Performans sınıflandırma DTO'su
    /// </summary>
    public class PerformanceClassificationDto
    {
        public string ClassificationCode { get; set; } = string.Empty;
        public string ClassificationName { get; set; } = string.Empty;
        public string ColorCode { get; set; } = string.Empty;
        public bool IsSuccessful { get; set; }
        public bool IsCritical { get; set; }
        public List<string> ApplicableActions { get; set; } = new();
    }

    /// <summary>
    /// Minimum puan gereksinimi doğrulama DTO'su
    /// </summary>
    public class MinimumScoreValidationDto
    {
        public bool IsValid { get; set; }
        public List<FailedRequirementDto> FailedRequirements { get; set; } = new();
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Başarısız gereksinim DTO'su
    /// </summary>
    public class FailedRequirementDto
    {
        public string CategoryName { get; set; } = string.Empty;
        public double RequiredScore { get; set; }
        public double ActualScore { get; set; }
        public string FailureAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Formül çalıştırma sonucu DTO'su
    /// </summary>
    public class FormulaExecutionResultDto
    {
        public string FormulaType { get; set; } = string.Empty;
        public double Result { get; set; }
        public Dictionary<string, object> IntermediateValues { get; set; } = new();
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Toplu hesaplama sonucu DTO'su
    /// </summary>
    public class BatchCalculationResultDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public int ProcessedCount { get; set; }
        public int SuccessfulCount { get; set; }
        public int FailedCount { get; set; }
        public List<OverallPerformanceDto> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan ProcessingTime { get; set; }
    }

    #endregion
}
