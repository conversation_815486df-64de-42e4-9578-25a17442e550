using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Performans konfigürasyon yönetim servisi implementasyonu
    /// </summary>
    public class PerformanceConfigurationService : IPerformanceConfigurationService
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly IMemoryCache _cache;
        private readonly ILogger<PerformanceConfigurationService> _logger;
        private readonly IConfigurationValidationService _validationService;
        private readonly IConfigurationAuditService _auditService;

        private const string CACHE_KEY_ACTIVE_CONFIG = "active_performance_config";
        private const string CACHE_KEY_CONFIG_PREFIX = "performance_config_";
        private const int CACHE_DURATION_MINUTES = 30;

        public PerformanceConfigurationService(
            AcademicPerformanceDbContext context,
            IMemoryCache cache,
            ILogger<PerformanceConfigurationService> logger,
            IConfigurationValidationService validationService,
            IConfigurationAuditService auditService)
        {
            _context = context;
            _cache = cache;
            _logger = logger;
            _validationService = validationService;
            _auditService = auditService;
        }

        #region Configuration Management

        public async Task<PerformanceConfigurationEntity?> GetActiveConfigurationAsync(DateTime effectiveDate)
        {
            var cacheKey = $"{CACHE_KEY_ACTIVE_CONFIG}_{effectiveDate:yyyyMMdd}";
            
            if (_cache.TryGetValue(cacheKey, out PerformanceConfigurationEntity? cachedConfig))
            {
                _logger.LogDebug("Active configuration retrieved from cache for date {EffectiveDate}", effectiveDate);
                return cachedConfig;
            }

            var config = await _context.PerformanceConfigurations
                .Include(pc => pc.CategoryWeights)
                .Include(pc => pc.PerformanceClassifications)
                    .ThenInclude(pcl => pcl.ClassificationActions)
                .Include(pc => pc.MinimumScoreRequirements)
                    .ThenInclude(msr => msr.Exceptions)
                .Include(pc => pc.CalculationFormulas)
                    .ThenInclude(cf => cf.FormulaVariables)
                .Include(pc => pc.EvaluationPeriods)
                    .ThenInclude(ep => ep.PeriodMilestones)
                .Where(pc => pc.IsActive && 
                           pc.ApprovalStatus == "Approved" &&
                           pc.EffectiveStartDate <= effectiveDate &&
                           (pc.EffectiveEndDate == null || pc.EffectiveEndDate >= effectiveDate))
                .OrderByDescending(pc => pc.EffectiveStartDate)
                .FirstOrDefaultAsync();

            if (config != null)
            {
                _cache.Set(cacheKey, config, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
                _logger.LogInformation("Active configuration {ConfigId} cached for date {EffectiveDate}", 
                    config.Id, effectiveDate);
            }
            else
            {
                _logger.LogWarning("No active configuration found for date {EffectiveDate}", effectiveDate);
            }

            return config;
        }

        public async Task<PerformanceConfigurationEntity?> GetConfigurationByIdAsync(string configurationId)
        {
            var cacheKey = $"{CACHE_KEY_CONFIG_PREFIX}{configurationId}";
            
            if (_cache.TryGetValue(cacheKey, out PerformanceConfigurationEntity? cachedConfig))
            {
                return cachedConfig;
            }

            var config = await _context.PerformanceConfigurations
                .Include(pc => pc.CategoryWeights)
                .Include(pc => pc.PerformanceClassifications)
                    .ThenInclude(pcl => pcl.ClassificationActions)
                .Include(pc => pc.MinimumScoreRequirements)
                    .ThenInclude(msr => msr.Exceptions)
                .Include(pc => pc.CalculationFormulas)
                    .ThenInclude(cf => cf.FormulaVariables)
                .Include(pc => pc.EvaluationPeriods)
                    .ThenInclude(ep => ep.PeriodMilestones)
                .FirstOrDefaultAsync(pc => pc.Id == configurationId);

            if (config != null)
            {
                _cache.Set(cacheKey, config, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return config;
        }

        public async Task<List<PerformanceConfigurationEntity>> GetAllConfigurationsAsync(bool includeInactive = false)
        {
            var query = _context.PerformanceConfigurations
                .Include(pc => pc.CategoryWeights)
                .Include(pc => pc.PerformanceClassifications)
                .Include(pc => pc.MinimumScoreRequirements)
                .Include(pc => pc.CalculationFormulas)
                .Include(pc => pc.EvaluationPeriods)
                .AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(pc => pc.IsActive);
            }

            return await query
                .OrderByDescending(pc => pc.CreatedAt)
                .ToListAsync();
        }

        public async Task<PerformanceConfigurationEntity> CreateConfigurationAsync(
            CreateConfigurationDto configuration, 
            string createdBy)
        {
            _logger.LogInformation("Creating new performance configuration {Name} by user {CreatedBy}", 
                configuration.Name, createdBy);

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Yeni konfigürasyon entity'si oluştur
                var configEntity = new PerformanceConfigurationEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = configuration.Name,
                    Description = configuration.Description,
                    Version = configuration.Version,
                    EffectiveStartDate = configuration.EffectiveStartDate,
                    EffectiveEndDate = configuration.EffectiveEndDate,
                    ApprovalStatus = "Draft",
                    CreatedBy = createdBy,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = false // Onaylanana kadar pasif
                };

                _context.PerformanceConfigurations.Add(configEntity);
                await _context.SaveChangesAsync();

                // Alt konfigürasyonları oluştur
                await CreateCategoryWeightsAsync(configEntity.Id, configuration.CategoryWeights);
                await CreatePerformanceClassificationsAsync(configEntity.Id, configuration.PerformanceClassifications);
                await CreateMinimumScoreRequirementsAsync(configEntity.Id, configuration.MinimumScoreRequirements);
                await CreateCalculationFormulasAsync(configEntity.Id, configuration.CalculationFormulas);
                await CreateEvaluationPeriodsAsync(configEntity.Id, configuration.EvaluationPeriods);

                // Doğrulama yap
                var fullConfig = await GetConfigurationByIdAsync(configEntity.Id);
                var validationResult = await _validationService.ValidateConfigurationAsync(fullConfig!);
                
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"Configuration validation failed: {string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage))}");
                }

                // Audit log
                await _auditService.LogConfigurationChangeAsync(
                    configEntity.Id, 
                    "Created", 
                    createdBy, 
                    "New configuration created");

                await transaction.CommitAsync();

                _logger.LogInformation("Performance configuration {ConfigId} created successfully", configEntity.Id);
                
                return fullConfig!;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating performance configuration {Name}", configuration.Name);
                throw;
            }
        }

        public async Task<PerformanceConfigurationEntity> UpdateConfigurationAsync(
            string configurationId, 
            UpdateConfigurationDto configuration, 
            string updatedBy)
        {
            _logger.LogInformation("Updating performance configuration {ConfigId} by user {UpdatedBy}", 
                configurationId, updatedBy);

            var existingConfig = await GetConfigurationByIdAsync(configurationId);
            if (existingConfig == null)
            {
                throw new InvalidOperationException($"Configuration {configurationId} not found");
            }

            if (existingConfig.ApprovalStatus == "Approved" && existingConfig.IsActive)
            {
                throw new InvalidOperationException("Cannot update an active approved configuration. Create a new version instead.");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Ana konfigürasyonu güncelle
                existingConfig.Name = configuration.Name;
                existingConfig.Description = configuration.Description;
                existingConfig.Version = configuration.Version;
                existingConfig.EffectiveStartDate = configuration.EffectiveStartDate;
                existingConfig.EffectiveEndDate = configuration.EffectiveEndDate;
                existingConfig.UpdatedBy = updatedBy;
                existingConfig.UpdatedAt = DateTime.UtcNow;
                existingConfig.ApprovalStatus = "Draft"; // Güncelleme sonrası tekrar onaya gönderilmeli

                // Alt konfigürasyonları güncelle
                await UpdateCategoryWeightsAsync(configurationId, configuration.CategoryWeights);
                await UpdatePerformanceClassificationsAsync(configurationId, configuration.PerformanceClassifications);
                await UpdateMinimumScoreRequirementsAsync(configurationId, configuration.MinimumScoreRequirements);
                await UpdateCalculationFormulasAsync(configurationId, configuration.CalculationFormulas);
                await UpdateEvaluationPeriodsAsync(configurationId, configuration.EvaluationPeriods);

                await _context.SaveChangesAsync();

                // Doğrulama yap
                var updatedConfig = await GetConfigurationByIdAsync(configurationId);
                var validationResult = await _validationService.ValidateConfigurationAsync(updatedConfig!);
                
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"Configuration validation failed: {string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage))}");
                }

                // Cache'i temizle
                await ClearConfigurationCacheAsync(configurationId);

                // Audit log
                await _auditService.LogConfigurationChangeAsync(
                    configurationId, 
                    "Updated", 
                    updatedBy, 
                    configuration.UpdateReason ?? "Configuration updated");

                await transaction.CommitAsync();

                _logger.LogInformation("Performance configuration {ConfigId} updated successfully", configurationId);
                
                return updatedConfig!;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error updating performance configuration {ConfigId}", configurationId);
                throw;
            }
        }

        public async Task<bool> DeleteConfigurationAsync(string configurationId, string deletedBy)
        {
            _logger.LogInformation("Deleting performance configuration {ConfigId} by user {DeletedBy}", 
                configurationId, deletedBy);

            var config = await GetConfigurationByIdAsync(configurationId);
            if (config == null)
            {
                return false;
            }

            if (config.IsActive)
            {
                throw new InvalidOperationException("Cannot delete an active configuration. Deactivate it first.");
            }

            try
            {
                // Soft delete
                config.IsActive = false;
                config.UpdatedBy = deletedBy;
                config.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Cache'i temizle
                await ClearConfigurationCacheAsync(configurationId);

                // Audit log
                await _auditService.LogConfigurationChangeAsync(
                    configurationId, 
                    "Deleted", 
                    deletedBy, 
                    "Configuration deleted");

                _logger.LogInformation("Performance configuration {ConfigId} deleted successfully", configurationId);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting performance configuration {ConfigId}", configurationId);
                return false;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task CreateCategoryWeightsAsync(string configurationId, List<CategoryWeightDto> categoryWeights)
        {
            foreach (var weight in categoryWeights)
            {
                var entity = new CategoryWeightEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    PerformanceConfigurationId = configurationId,
                    CategoryId = weight.CategoryId,
                    CategoryName = weight.CategoryName,
                    CategoryCode = weight.CategoryCode,
                    Weight = (decimal)weight.Weight,
                    WeightPercentage = (decimal)(weight.Weight * 100),
                    Description = weight.Description,
                    DisplayOrder = weight.DisplayOrder,
                    RequiresNormalization = weight.RequiresNormalization,
                    NormalizationType = weight.NormalizationType,
                    IsActive = weight.IsActive,
                    CreatedAt = DateTime.UtcNow
                };

                _context.CategoryWeights.Add(entity);
            }
        }

        private async Task CreatePerformanceClassificationsAsync(string configurationId, List<PerformanceClassificationDto> classifications)
        {
            foreach (var classification in classifications)
            {
                var entity = new PerformanceClassificationEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    PerformanceConfigurationId = configurationId,
                    ClassificationCode = classification.ClassificationCode,
                    ClassificationName = classification.ClassificationName,
                    MinValue = (decimal)classification.MinValue,
                    MaxValue = (decimal)classification.MaxValue,
                    Description = classification.Description,
                    ColorCode = classification.ColorCode,
                    DisplayOrder = classification.DisplayOrder,
                    IsSuccessful = classification.IsSuccessful,
                    IsCritical = classification.IsCritical,
                    SpecialRules = classification.SpecialRules,
                    IsActive = classification.IsActive,
                    CreatedAt = DateTime.UtcNow
                };

                _context.PerformanceClassifications.Add(entity);
            }
        }

        // Diğer helper methodlar benzer şekilde implement edilecek...

        #endregion
    }
}
