using AcademicPerformance.Models.Entities;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Formül çalıştırma servisi interface'i
    /// </summary>
    public interface IFormulaExecutionService
    {
        Task<FormulaResult> ExecuteFormulaAsync(CalculationFormulaEntity formula, Dictionary<string, object> parameters);
        Task<bool> ValidateFormulaAsync(string formulaExpression, Dictionary<string, object> testParameters);
    }

    /// <summary>
    /// Formül çalıştırma servisi implementasyonu
    /// </summary>
    public class FormulaExecutionService : IFormulaExecutionService
    {
        private readonly ILogger<FormulaExecutionService> _logger;
        private readonly Dictionary<string, Func<Dictionary<string, object>, double>> _builtInFunctions;

        public FormulaExecutionService(ILogger<FormulaExecutionService> logger)
        {
            _logger = logger;
            _builtInFunctions = InitializeBuiltInFunctions();
        }

        public async Task<FormulaResult> ExecuteFormulaAsync(
            CalculationFormulaEntity formula, 
            Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation("Executing formula {FormulaName} of type {FormulaType}", 
                    formula.FormulaName, formula.FormulaType);

                var result = new FormulaResult
                {
                    FormulaName = formula.FormulaName,
                    FormulaType = formula.FormulaType
                };

                // Formül parametrelerini birleştir
                var allParameters = new Dictionary<string, object>(parameters);
                if (!string.IsNullOrEmpty(formula.Parameters))
                {
                    var formulaParams = JsonSerializer.Deserialize<Dictionary<string, object>>(formula.Parameters);
                    if (formulaParams != null)
                    {
                        foreach (var param in formulaParams)
                        {
                            allParameters.TryAdd(param.Key, param.Value);
                        }
                    }
                }

                // Formül türüne göre hesaplama yap
                result.Value = formula.FormulaType switch
                {
                    "OverallScore" => await CalculateOverallScoreAsync(formula.FormulaExpression, allParameters),
                    "PerformanceIndex" => await CalculatePerformanceIndexAsync(formula.FormulaExpression, allParameters),
                    "SuccessIndex" => await CalculateSuccessIndexAsync(formula.FormulaExpression, allParameters),
                    "CategoryNormalization" => await CalculateCategoryNormalizationAsync(formula.FormulaExpression, allParameters),
                    _ => await ExecuteGenericFormulaAsync(formula.FormulaExpression, allParameters)
                };

                result.IsSuccessful = true;
                result.IntermediateValues = allParameters;

                _logger.LogInformation("Formula {FormulaName} executed successfully. Result: {Result}", 
                    formula.FormulaName, result.Value);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing formula {FormulaName}", formula.FormulaName);
                
                return new FormulaResult
                {
                    FormulaName = formula.FormulaName,
                    FormulaType = formula.FormulaType,
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> ValidateFormulaAsync(string formulaExpression, Dictionary<string, object> testParameters)
        {
            try
            {
                await ExecuteGenericFormulaAsync(formulaExpression, testParameters);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #region Formula Type Implementations

        private async Task<double> CalculateOverallScoreAsync(string expression, Dictionary<string, object> parameters)
        {
            // Ağırlıklı toplam hesaplama
            if (expression.Contains("WEIGHTED_SUM"))
            {
                if (parameters.TryGetValue("categoryScores", out var scoresObj) && 
                    scoresObj is IEnumerable<dynamic> scores)
                {
                    double total = 0;
                    foreach (var score in scores)
                    {
                        var weightedScore = GetPropertyValue(score, "WeightedScore");
                        total += Convert.ToDouble(weightedScore);
                    }
                    return total;
                }
            }

            return await ExecuteGenericFormulaAsync(expression, parameters);
        }

        private async Task<double> CalculatePerformanceIndexAsync(string expression, Dictionary<string, object> parameters)
        {
            // Yönergeye göre: Performans Endeksi = 1 - ((n-1)/N)
            if (expression.Contains("PERFORMANCE_INDEX"))
            {
                if (parameters.TryGetValue("rank", out var rankObj) && 
                    parameters.TryGetValue("totalCount", out var totalObj))
                {
                    var rank = Convert.ToInt32(rankObj);
                    var total = Convert.ToInt32(totalObj);
                    
                    return total > 0 ? 1.0 - ((double)(rank - 1) / total) : 0.0;
                }
            }

            return await ExecuteGenericFormulaAsync(expression, parameters);
        }

        private async Task<double> CalculateSuccessIndexAsync(string expression, Dictionary<string, object> parameters)
        {
            // Yönergeye göre: Başarı Endeksi = (ntop-n+1)/ntop
            if (expression.Contains("SUCCESS_INDEX"))
            {
                if (parameters.TryGetValue("rank", out var rankObj) && 
                    parameters.TryGetValue("totalCount", out var totalObj))
                {
                    var rank = Convert.ToInt32(rankObj);
                    var total = Convert.ToInt32(totalObj);
                    
                    return total > 0 ? (double)(total - rank + 1) / total : 0.0;
                }
            }

            return await ExecuteGenericFormulaAsync(expression, parameters);
        }

        private async Task<double> CalculateCategoryNormalizationAsync(string expression, Dictionary<string, object> parameters)
        {
            // Normalizasyon hesaplamaları
            if (expression.Contains("MIN_MAX_NORMALIZE"))
            {
                if (parameters.TryGetValue("value", out var valueObj) &&
                    parameters.TryGetValue("minValue", out var minObj) &&
                    parameters.TryGetValue("maxValue", out var maxObj))
                {
                    var value = Convert.ToDouble(valueObj);
                    var min = Convert.ToDouble(minObj);
                    var max = Convert.ToDouble(maxObj);
                    
                    return max > min ? (value - min) / (max - min) : 0.0;
                }
            }

            return await ExecuteGenericFormulaAsync(expression, parameters);
        }

        private async Task<double> ExecuteGenericFormulaAsync(string expression, Dictionary<string, object> parameters)
        {
            // Basit matematiksel ifade değerlendirici
            // Gerçek implementasyonda daha gelişmiş bir parser kullanılabilir
            
            var processedExpression = expression;
            
            // Parametreleri değiştir
            foreach (var param in parameters)
            {
                var placeholder = $"{{{param.Key}}}";
                if (processedExpression.Contains(placeholder))
                {
                    var value = Convert.ToDouble(param.Value);
                    processedExpression = processedExpression.Replace(placeholder, value.ToString("F6"));
                }
            }

            // Built-in fonksiyonları çalıştır
            foreach (var func in _builtInFunctions)
            {
                if (processedExpression.Contains(func.Key))
                {
                    var result = func.Value(parameters);
                    processedExpression = processedExpression.Replace(func.Key, result.ToString("F6"));
                }
            }

            // Basit matematiksel değerlendirme
            return EvaluateMathExpression(processedExpression);
        }

        #endregion

        #region Helper Methods

        private Dictionary<string, Func<Dictionary<string, object>, double>> InitializeBuiltInFunctions()
        {
            return new Dictionary<string, Func<Dictionary<string, object>, double>>
            {
                ["SUM"] = (params) => params.Values.Where(v => v is double || v is int).Sum(v => Convert.ToDouble(v)),
                ["AVG"] = (params) => params.Values.Where(v => v is double || v is int).Average(v => Convert.ToDouble(v)),
                ["MAX"] = (params) => params.Values.Where(v => v is double || v is int).Max(v => Convert.ToDouble(v)),
                ["MIN"] = (params) => params.Values.Where(v => v is double || v is int).Min(v => Convert.ToDouble(v)),
                ["COUNT"] = (params) => params.Count
            };
        }

        private object GetPropertyValue(dynamic obj, string propertyName)
        {
            var type = obj.GetType();
            var property = type.GetProperty(propertyName);
            return property?.GetValue(obj) ?? 0;
        }

        private double EvaluateMathExpression(string expression)
        {
            // Basit matematiksel ifade değerlendirici
            // Gerçek implementasyonda NCalc veya benzeri bir kütüphane kullanılabilir
            
            try
            {
                // Güvenlik için sadece sayılar ve temel operatörlere izin ver
                var sanitized = Regex.Replace(expression, @"[^0-9+\-*/().\s]", "");
                
                // System.Data.DataTable.Compute kullanarak basit hesaplama
                var table = new System.Data.DataTable();
                var result = table.Compute(sanitized, null);
                
                return Convert.ToDouble(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error evaluating math expression: {Expression}", expression);
                return 0.0;
            }
        }

        #endregion
    }

    /// <summary>
    /// Formül çalıştırma sonucu
    /// </summary>
    public class FormulaResult
    {
        public string FormulaName { get; set; } = string.Empty;
        public string FormulaType { get; set; } = string.Empty;
        public double Value { get; set; }
        public Dictionary<string, object> IntermediateValues { get; set; } = new();
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
