using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Entities;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Generic performans hesaplama motoru implementasyonu
    /// </summary>
    public class PerformanceCalculationEngine : IPerformanceCalculationEngine
    {
        private readonly IPerformanceConfigurationService _configurationService;
        private readonly IFormulaExecutionService _formulaService;
        private readonly INormalizationService _normalizationService;
        private readonly IReportingStore _reportingStore;
        private readonly IMemoryCache _cache;
        private readonly ILogger<PerformanceCalculationEngine> _logger;

        private const string CACHE_KEY_ACTIVE_CONFIG = "active_performance_config";
        private const int CACHE_DURATION_MINUTES = 30;

        public PerformanceCalculationEngine(
            IPerformanceConfigurationService configurationService,
            IFormulaExecutionService formulaService,
            INormalizationService normalizationService,
            IReportingStore reportingStore,
            IMemoryCache cache,
            ILogger<PerformanceCalculationEngine> logger)
        {
            _configurationService = configurationService;
            _formulaService = formulaService;
            _normalizationService = normalizationService;
            _reportingStore = reportingStore;
            _cache = cache;
            _logger = logger;
        }

        #region Configuration Management

        public async Task<PerformanceConfigurationEntity?> GetActiveConfigurationAsync(DateTime? effectiveDate = null)
        {
            var cacheKey = $"{CACHE_KEY_ACTIVE_CONFIG}_{effectiveDate?.ToString("yyyyMMdd") ?? "current"}";

            if (_cache.TryGetValue(cacheKey, out PerformanceConfigurationEntity? cachedConfig))
            {
                return cachedConfig;
            }

            var config = await _configurationService.GetActiveConfigurationAsync(effectiveDate ?? DateTime.UtcNow);

            if (config != null)
            {
                _cache.Set(cacheKey, config, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return config;
        }

        public async Task<PerformanceConfigurationEntity?> GetConfigurationAsync(string configurationId)
        {
            var cacheKey = $"performance_config_{configurationId}";

            if (_cache.TryGetValue(cacheKey, out PerformanceConfigurationEntity? cachedConfig))
            {
                return cachedConfig;
            }

            var config = await _configurationService.GetConfigurationByIdAsync(configurationId);

            if (config != null)
            {
                _cache.Set(cacheKey, config, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return config;
        }

        #endregion

        #region Score Calculations

        public async Task<List<CategoryPerformanceDto>> CalculateCategoryScoresAsync(
            string academicianUserId,
            string configurationId,
            DateTime startDate,
            DateTime endDate)
        {
            _logger.LogInformation("Calculating category scores for academician {UserId} with config {ConfigId}",
                academicianUserId, configurationId);

            var config = await GetConfigurationAsync(configurationId);
            if (config == null)
            {
                throw new InvalidOperationException($"Configuration {configurationId} not found");
            }

            var categoryScores = new List<CategoryPerformanceDto>();

            foreach (var categoryWeight in config.CategoryWeights.Where(cw => cw.IsActive))
            {
                try
                {
                    // Ham puanı hesapla
                    var rawScore = await CalculateRawCategoryScoreAsync(
                        academicianUserId,
                        categoryWeight.CategoryId,
                        startDate,
                        endDate);

                    // Normalizasyon gerekiyorsa uygula
                    var normalizedScore = rawScore;
                    if (categoryWeight.RequiresNormalization && !string.IsNullOrEmpty(categoryWeight.NormalizationType))
                    {
                        normalizedScore = await _normalizationService.NormalizeScoreAsync(
                            rawScore,
                            categoryWeight.CategoryId,
                            categoryWeight.NormalizationType,
                            startDate,
                            endDate);
                    }

                    categoryScores.Add(new CategoryPerformanceDto
                    {
                        CategoryId = categoryWeight.CategoryId,
                        CategoryName = categoryWeight.CategoryName,
                        CategoryCode = categoryWeight.CategoryCode,
                        RawScore = rawScore,
                        NormalizedScore = normalizedScore,
                        Weight = (double)categoryWeight.Weight,
                        WeightedScore = normalizedScore * (double)categoryWeight.Weight,
                        MaxPossibleScore = 100.0 // Bu değer konfigürasyondan gelebilir
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error calculating score for category {CategoryId}", categoryWeight.CategoryId);

                    // Hata durumunda 0 puan ver
                    categoryScores.Add(new CategoryPerformanceDto
                    {
                        CategoryId = categoryWeight.CategoryId,
                        CategoryName = categoryWeight.CategoryName,
                        CategoryCode = categoryWeight.CategoryCode,
                        RawScore = 0,
                        NormalizedScore = 0,
                        Weight = (double)categoryWeight.Weight,
                        WeightedScore = 0,
                        MaxPossibleScore = 100.0,
                        HasError = true,
                        ErrorMessage = ex.Message
                    });
                }
            }

            return categoryScores;
        }

        public async Task<OverallPerformanceDto> CalculateOverallScoreAsync(
            string academicianUserId,
            string configurationId,
            DateTime startDate,
            DateTime endDate)
        {
            _logger.LogInformation("Calculating overall performance for academician {UserId}", academicianUserId);

            var categoryScores = await CalculateCategoryScoresAsync(academicianUserId, configurationId, startDate, endDate);

            // Ağırlıklı toplam skoru hesapla
            var weightedScore = categoryScores.Sum(cs => cs.WeightedScore);

            // Performans endeksini hesapla (konfigürasyondan formül kullanarak)
            var performanceIndex = await CalculatePerformanceIndexAsync(categoryScores, configurationId);

            // Performans seviyesini belirle
            var classification = await DeterminePerformanceLevelAsync(performanceIndex, configurationId);

            // Minimum puan gereksinimlerini kontrol et
            var validation = await ValidateMinimumScoreRequirementsAsync(categoryScores, configurationId, academicianUserId);

            return new OverallPerformanceDto
            {
                AcademicianUserId = academicianUserId,
                WeightedScore = weightedScore,
                PerformanceIndex = performanceIndex,
                PerformanceLevel = classification.ClassificationCode,
                CategoryScores = categoryScores,
                MeetsMinimumRequirements = validation.IsValid,
                FailedRequirements = validation.FailedRequirements.Select(fr => fr.CategoryName).ToList(),
                CalculatedAt = DateTime.UtcNow
            };
        }

        public async Task<List<NormalizedPerformanceDto>> CalculateDepartmentNormalizationAsync(
            string departmentId,
            string configurationId,
            DateTime startDate,
            DateTime endDate)
        {
            _logger.LogInformation("Calculating department normalization for {DepartmentId}", departmentId);

            var config = await GetConfigurationAsync(configurationId);
            if (config == null)
            {
                throw new InvalidOperationException($"Configuration {configurationId} not found");
            }

            var departmentAcademicians = await _reportingStore.GetDepartmentAcademiciansAsync(departmentId);
            var normalizedResults = new List<NormalizedPerformanceDto>();

            foreach (var categoryWeight in config.CategoryWeights.Where(cw => cw.IsActive && cw.RequiresNormalization))
            {
                var categoryScores = new List<(string UserId, double Score)>();

                // Tüm akademisyenler için kategori skorlarını hesapla
                foreach (var academician in departmentAcademicians)
                {
                    var rawScore = await CalculateRawCategoryScoreAsync(
                        academician.UserId,
                        categoryWeight.CategoryId,
                        startDate,
                        endDate);

                    categoryScores.Add((academician.UserId, rawScore));
                }

                // Normalizasyon uygula
                var normalizedScores = await _normalizationService.NormalizeCategoryScoresAsync(
                    categoryScores,
                    categoryWeight.NormalizationType ?? "Ranking");

                normalizedResults.AddRange(normalizedScores.Select(ns => new NormalizedPerformanceDto
                {
                    AcademicianUserId = ns.UserId,
                    CategoryId = categoryWeight.CategoryId,
                    RawScore = ns.RawScore,
                    NormalizedScore = ns.NormalizedScore,
                    Rank = ns.Rank,
                    TotalCount = categoryScores.Count,
                    SuccessIndex = ns.SuccessIndex
                }));
            }

            return normalizedResults;
        }

        public async Task<PerformanceClassificationDto> DeterminePerformanceLevelAsync(
            double performanceIndex,
            string configurationId)
        {
            var config = await GetConfigurationAsync(configurationId);
            if (config == null)
            {
                throw new InvalidOperationException($"Configuration {configurationId} not found");
            }

            var classification = config.PerformanceClassifications
                .Where(pc => pc.IsActive && performanceIndex >= (double)pc.MinValue && performanceIndex <= (double)pc.MaxValue)
                .OrderBy(pc => pc.DisplayOrder)
                .FirstOrDefault();

            if (classification == null)
            {
                // Varsayılan sınıflandırma (E kategorisi)
                classification = config.PerformanceClassifications
                    .Where(pc => pc.IsActive && pc.ClassificationCode == "E")
                    .FirstOrDefault();
            }

            if (classification == null)
            {
                throw new InvalidOperationException("No valid performance classification found");
            }

            return new PerformanceClassificationDto
            {
                ClassificationCode = classification.ClassificationCode,
                ClassificationName = classification.ClassificationName,
                ColorCode = classification.ColorCode ?? "#000000",
                IsSuccessful = classification.IsSuccessful,
                IsCritical = classification.IsCritical,
                ApplicableActions = classification.ClassificationActions
                    .Where(ca => ca.IsActive)
                    .Select(ca => ca.ActionDescription)
                    .ToList()
            };
        }

        public async Task<MinimumScoreValidationDto> ValidateMinimumScoreRequirementsAsync(
            List<CategoryPerformanceDto> categoryScores,
            string configurationId,
            string academicianUserId)
        {
            var config = await GetConfigurationAsync(configurationId);
            if (config == null)
            {
                throw new InvalidOperationException($"Configuration {configurationId} not found");
            }

            var failedRequirements = new List<FailedRequirementDto>();

            foreach (var requirement in config.MinimumScoreRequirements.Where(msr => msr.IsActive && msr.IsMandatory))
            {
                var categoryScore = categoryScores.FirstOrDefault(cs => cs.CategoryId == requirement.CategoryWeight.CategoryId);
                if (categoryScore == null) continue;

                var scoreToCheck = requirement.ScoreType switch
                {
                    "Raw" => categoryScore.RawScore,
                    "Normalized" => categoryScore.NormalizedScore,
                    "Percentage" => categoryScore.NormalizedScore,
                    _ => categoryScore.NormalizedScore
                };

                if (scoreToCheck < (double)requirement.MinimumScore)
                {
                    failedRequirements.Add(new FailedRequirementDto
                    {
                        CategoryName = requirement.CategoryWeight.CategoryName,
                        RequiredScore = (double)requirement.MinimumScore,
                        ActualScore = scoreToCheck,
                        FailureAction = requirement.FailureAction ?? "Warning"
                    });
                }
            }

            var recommendedAction = failedRequirements.Any(fr => fr.FailureAction == "AutoClassifyAsE")
                ? "AutoClassifyAsE"
                : failedRequirements.Any() ? "RequireApproval" : "None";

            return new MinimumScoreValidationDto
            {
                IsValid = !failedRequirements.Any(),
                FailedRequirements = failedRequirements,
                RecommendedAction = recommendedAction
            };
        }

        public async Task<FormulaExecutionResultDto> ExecuteFormulaAsync(
            string formulaType,
            string configurationId,
            Dictionary<string, object> parameters)
        {
            try
            {
                var config = await GetConfigurationAsync(configurationId);
                if (config == null)
                {
                    return new FormulaExecutionResultDto
                    {
                        FormulaType = formulaType,
                        IsSuccessful = false,
                        ErrorMessage = $"Configuration {configurationId} not found"
                    };
                }

                var formula = config.CalculationFormulas
                    .FirstOrDefault(cf => cf.IsActive && cf.FormulaType == formulaType);

                if (formula == null)
                {
                    return new FormulaExecutionResultDto
                    {
                        FormulaType = formulaType,
                        IsSuccessful = false,
                        ErrorMessage = $"Formula type {formulaType} not found"
                    };
                }

                var result = await _formulaService.ExecuteFormulaAsync(formula, parameters);

                return new FormulaExecutionResultDto
                {
                    FormulaType = formulaType,
                    Result = result.Value,
                    IntermediateValues = result.IntermediateValues,
                    IsSuccessful = result.IsSuccessful,
                    ErrorMessage = result.ErrorMessage
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing formula {FormulaType}", formulaType);

                return new FormulaExecutionResultDto
                {
                    FormulaType = formulaType,
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<BatchCalculationResultDto> CalculateDepartmentPerformanceBatchAsync(
            string departmentId,
            string configurationId,
            DateTime startDate,
            DateTime endDate)
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Starting batch calculation for department {DepartmentId}", departmentId);

            var result = new BatchCalculationResultDto
            {
                DepartmentId = departmentId
            };

            try
            {
                var academicians = await _reportingStore.GetDepartmentAcademiciansAsync(departmentId);
                result.ProcessedCount = academicians.Count;

                var tasks = academicians.Select(async academician =>
                {
                    try
                    {
                        var performance = await CalculateOverallScoreAsync(
                            academician.UserId,
                            configurationId,
                            startDate,
                            endDate);

                        return (IsSuccessful: true, Performance: performance, Error: (string?)null);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error calculating performance for academician {UserId}", academician.UserId);
                        return (IsSuccessful: false, Performance: (OverallPerformanceDto?)null, Error: ex.Message);
                    }
                });

                var results = await Task.WhenAll(tasks);

                result.SuccessfulCount = results.Count(r => r.IsSuccessful);
                result.FailedCount = results.Count(r => !r.IsSuccessful);
                result.Results = results.Where(r => r.IsSuccessful).Select(r => r.Performance!).ToList();
                result.Errors = results.Where(r => !r.IsSuccessful).Select(r => r.Error!).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch calculation for department {DepartmentId}", departmentId);
                result.Errors.Add(ex.Message);
            }

            stopwatch.Stop();
            result.ProcessingTime = stopwatch.Elapsed;

            _logger.LogInformation("Batch calculation completed for department {DepartmentId}. " +
                "Processed: {ProcessedCount}, Successful: {SuccessfulCount}, Failed: {FailedCount}, " +
                "Duration: {Duration}ms",
                departmentId, result.ProcessedCount, result.SuccessfulCount, result.FailedCount,
                result.ProcessingTime.TotalMilliseconds);

            return result;
        }

        #endregion

        #region Private Helper Methods

        private async Task<double> CalculateRawCategoryScoreAsync(
            string academicianUserId,
            string categoryId,
            DateTime startDate,
            DateTime endDate)
        {
            // Mevcut ReportingStore'dan veri çek
            var categoryData = await _reportingStore.GetCategoryDataAsync(academicianUserId, categoryId, startDate, endDate);

            if (categoryData == null || !categoryData.Any())
            {
                return 0.0;
            }

            // Basit hesaplama - geliştirilmesi gerekebilir
            var totalCriteria = categoryData.Sum(cd => cd.TotalCriteria);
            var completedCriteria = categoryData.Sum(cd => cd.CompletedCriteria);

            return totalCriteria > 0 ? (double)completedCriteria / totalCriteria * 100 : 0.0;
        }

        private async Task<double> CalculatePerformanceIndexAsync(
            List<CategoryPerformanceDto> categoryScores,
            string configurationId)
        {
            // Performans endeksi formülünü çalıştır
            var parameters = new Dictionary<string, object>
            {
                ["categoryScores"] = categoryScores,
                ["totalWeightedScore"] = categoryScores.Sum(cs => cs.WeightedScore)
            };

            var result = await ExecuteFormulaAsync("PerformanceIndex", configurationId, parameters);
            return result.IsSuccessful ? result.Result : categoryScores.Sum(cs => cs.WeightedScore) / 100.0;
        }

        #endregion
    }
}
