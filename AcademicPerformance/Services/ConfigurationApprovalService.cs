using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Konfigürasyon onay süreci yönetim servisi
    /// </summary>
    public interface IConfigurationApprovalService
    {
        Task<bool> SubmitForApprovalAsync(string configurationId, string submittedBy, List<string> approvers);
        Task<bool> ApproveConfigurationAsync(string configurationId, string approvedBy, string? comments = null);
        Task<bool> RejectConfigurationAsync(string configurationId, string rejectedBy, string reason);
        Task<List<ConfigurationApprovalEntity>> GetPendingApprovalsAsync(string approverId);
        Task<List<ConfigurationApprovalEntity>> GetApprovalHistoryAsync(string configurationId);
    }

    /// <summary>
    /// Konfigürasyon onay süreci implementasyonu
    /// </summary>
    public class ConfigurationApprovalService : IConfigurationApprovalService
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<ConfigurationApprovalService> _logger;
        private readonly INotificationService _notificationService;
        private readonly IConfigurationAuditService _auditService;

        public ConfigurationApprovalService(
            AcademicPerformanceDbContext context,
            ILogger<ConfigurationApprovalService> logger,
            INotificationService notificationService,
            IConfigurationAuditService auditService)
        {
            _context = context;
            _logger = logger;
            _notificationService = notificationService;
            _auditService = auditService;
        }

        public async Task<bool> SubmitForApprovalAsync(
            string configurationId, 
            string submittedBy, 
            List<string> approvers)
        {
            _logger.LogInformation("Submitting configuration {ConfigId} for approval by {SubmittedBy}", 
                configurationId, submittedBy);

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var config = await _context.PerformanceConfigurations
                    .FirstOrDefaultAsync(pc => pc.Id == configurationId);

                if (config == null)
                {
                    _logger.LogWarning("Configuration {ConfigId} not found", configurationId);
                    return false;
                }

                if (config.ApprovalStatus != "Draft")
                {
                    _logger.LogWarning("Configuration {ConfigId} is not in Draft status. Current status: {Status}", 
                        configurationId, config.ApprovalStatus);
                    return false;
                }

                // Konfigürasyon durumunu güncelle
                config.ApprovalStatus = "Pending";
                config.UpdatedBy = submittedBy;
                config.UpdatedAt = DateTime.UtcNow;

                // Onay kayıtları oluştur
                foreach (var approverId in approvers)
                {
                    var approvalEntity = new ConfigurationApprovalEntity
                    {
                        Id = Guid.NewGuid().ToString(),
                        PerformanceConfigurationId = configurationId,
                        ApproverId = approverId,
                        ApprovalStatus = "Pending",
                        SubmittedBy = submittedBy,
                        SubmittedAt = DateTime.UtcNow,
                        ApprovalOrder = approvers.IndexOf(approverId) + 1,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.ConfigurationApprovals.Add(approvalEntity);
                }

                await _context.SaveChangesAsync();

                // Audit log
                await _auditService.LogConfigurationChangeAsync(
                    configurationId, 
                    "SubmittedForApproval", 
                    submittedBy, 
                    $"Submitted for approval to {approvers.Count} approvers");

                // Onaylayıcılara bildirim gönder
                foreach (var approverId in approvers)
                {
                    await _notificationService.SendApprovalRequestNotificationAsync(
                        approverId, 
                        configurationId, 
                        config.Name);
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Configuration {ConfigId} submitted for approval successfully", configurationId);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error submitting configuration {ConfigId} for approval", configurationId);
                return false;
            }
        }

        public async Task<bool> ApproveConfigurationAsync(
            string configurationId, 
            string approvedBy, 
            string? comments = null)
        {
            _logger.LogInformation("Approving configuration {ConfigId} by {ApprovedBy}", 
                configurationId, approvedBy);

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var config = await _context.PerformanceConfigurations
                    .FirstOrDefaultAsync(pc => pc.Id == configurationId);

                if (config == null)
                {
                    _logger.LogWarning("Configuration {ConfigId} not found", configurationId);
                    return false;
                }

                var approval = await _context.ConfigurationApprovals
                    .FirstOrDefaultAsync(ca => ca.PerformanceConfigurationId == configurationId && 
                                             ca.ApproverId == approvedBy && 
                                             ca.ApprovalStatus == "Pending");

                if (approval == null)
                {
                    _logger.LogWarning("No pending approval found for configuration {ConfigId} and approver {ApprovedBy}", 
                        configurationId, approvedBy);
                    return false;
                }

                // Onay kaydını güncelle
                approval.ApprovalStatus = "Approved";
                approval.ApprovedAt = DateTime.UtcNow;
                approval.Comments = comments;

                // Tüm onaylar tamamlandı mı kontrol et
                var allApprovals = await _context.ConfigurationApprovals
                    .Where(ca => ca.PerformanceConfigurationId == configurationId && ca.IsActive)
                    .ToListAsync();

                var allApproved = allApprovals.All(a => a.ApprovalStatus == "Approved");

                if (allApproved)
                {
                    // Konfigürasyonu onaylanmış olarak işaretle
                    config.ApprovalStatus = "Approved";
                    config.ApprovedBy = approvedBy;
                    config.ApprovedAt = DateTime.UtcNow;

                    _logger.LogInformation("All approvals completed for configuration {ConfigId}", configurationId);

                    // Konfigürasyon sahibine bildirim gönder
                    await _notificationService.SendApprovalCompletedNotificationAsync(
                        config.CreatedBy, 
                        configurationId, 
                        config.Name);
                }

                await _context.SaveChangesAsync();

                // Audit log
                await _auditService.LogConfigurationChangeAsync(
                    configurationId, 
                    "Approved", 
                    approvedBy, 
                    comments ?? "Configuration approved");

                await transaction.CommitAsync();

                _logger.LogInformation("Configuration {ConfigId} approved by {ApprovedBy}", configurationId, approvedBy);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error approving configuration {ConfigId}", configurationId);
                return false;
            }
        }

        public async Task<bool> RejectConfigurationAsync(
            string configurationId, 
            string rejectedBy, 
            string reason)
        {
            _logger.LogInformation("Rejecting configuration {ConfigId} by {RejectedBy}", 
                configurationId, rejectedBy);

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var config = await _context.PerformanceConfigurations
                    .FirstOrDefaultAsync(pc => pc.Id == configurationId);

                if (config == null)
                {
                    _logger.LogWarning("Configuration {ConfigId} not found", configurationId);
                    return false;
                }

                var approval = await _context.ConfigurationApprovals
                    .FirstOrDefaultAsync(ca => ca.PerformanceConfigurationId == configurationId && 
                                             ca.ApproverId == rejectedBy && 
                                             ca.ApprovalStatus == "Pending");

                if (approval == null)
                {
                    _logger.LogWarning("No pending approval found for configuration {ConfigId} and approver {RejectedBy}", 
                        configurationId, rejectedBy);
                    return false;
                }

                // Onay kaydını güncelle
                approval.ApprovalStatus = "Rejected";
                approval.ApprovedAt = DateTime.UtcNow;
                approval.Comments = reason;

                // Konfigürasyonu reddedilmiş olarak işaretle
                config.ApprovalStatus = "Rejected";
                config.UpdatedBy = rejectedBy;
                config.UpdatedAt = DateTime.UtcNow;

                // Diğer bekleyen onayları iptal et
                var otherApprovals = await _context.ConfigurationApprovals
                    .Where(ca => ca.PerformanceConfigurationId == configurationId && 
                               ca.ApprovalStatus == "Pending" && 
                               ca.Id != approval.Id)
                    .ToListAsync();

                foreach (var otherApproval in otherApprovals)
                {
                    otherApproval.ApprovalStatus = "Cancelled";
                    otherApproval.Comments = "Cancelled due to rejection by another approver";
                }

                await _context.SaveChangesAsync();

                // Audit log
                await _auditService.LogConfigurationChangeAsync(
                    configurationId, 
                    "Rejected", 
                    rejectedBy, 
                    reason);

                // Konfigürasyon sahibine bildirim gönder
                await _notificationService.SendApprovalRejectedNotificationAsync(
                    config.CreatedBy, 
                    configurationId, 
                    config.Name, 
                    reason);

                await transaction.CommitAsync();

                _logger.LogInformation("Configuration {ConfigId} rejected by {RejectedBy}", configurationId, rejectedBy);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error rejecting configuration {ConfigId}", configurationId);
                return false;
            }
        }

        public async Task<List<ConfigurationApprovalEntity>> GetPendingApprovalsAsync(string approverId)
        {
            return await _context.ConfigurationApprovals
                .Include(ca => ca.PerformanceConfiguration)
                .Where(ca => ca.ApproverId == approverId && 
                           ca.ApprovalStatus == "Pending" && 
                           ca.IsActive)
                .OrderBy(ca => ca.SubmittedAt)
                .ToListAsync();
        }

        public async Task<List<ConfigurationApprovalEntity>> GetApprovalHistoryAsync(string configurationId)
        {
            return await _context.ConfigurationApprovals
                .Where(ca => ca.PerformanceConfigurationId == configurationId)
                .OrderBy(ca => ca.ApprovalOrder)
                .ThenBy(ca => ca.SubmittedAt)
                .ToListAsync();
        }
    }

    /// <summary>
    /// Konfigürasyon onay entity'si
    /// </summary>
    public class ConfigurationApprovalEntity : EntityBaseModel
    {
        public string PerformanceConfigurationId { get; set; } = string.Empty;
        public string ApproverId { get; set; } = string.Empty;
        public string ApprovalStatus { get; set; } = "Pending"; // Pending, Approved, Rejected, Cancelled
        public string SubmittedBy { get; set; } = string.Empty;
        public DateTime SubmittedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string? Comments { get; set; }
        public int ApprovalOrder { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual PerformanceConfigurationEntity PerformanceConfiguration { get; set; } = null!;
    }
}
